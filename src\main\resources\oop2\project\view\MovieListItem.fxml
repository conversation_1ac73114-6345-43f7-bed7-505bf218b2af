<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.*?>
<?import org.kordamp.ikonli.javafx.FontIcon?>

<?scenebuilder-stylesheet ../css/MovieListItem.css?>

<HBox xmlns="http://javafx.com/javafx/17.0.12"
      xmlns:fx="http://javafx.com/fxml/1"
      styleClass="movie-list-item"
      spacing="20.0"
      alignment="CENTER_LEFT">

    <padding>
        <Insets top="15.0" bottom="15.0" left="20.0" right="20.0"/>
    </padding>

    <!-- Movie Poster -->
    <StackPane styleClass="list-poster-container">
        <ImageView fx:id="moviePosterImage" 
                  fitWidth="80.0" 
                  fitHeight="120.0" 
                  preserveRatio="true"
                  styleClass="list-movie-poster"/>
        
        <VBox fx:id="posterPlaceholder" 
              alignment="CENTER" 
              spacing="5.0"
              styleClass="list-poster-placeholder"
              visible="false">
            <FontIcon iconLiteral="fas-film" iconSize="24" styleClass="list-placeholder-icon"/>
        </VBox>
    </StackPane>

    <!-- Movie Information -->
    <VBox spacing="8.0" HBox.hgrow="ALWAYS" styleClass="movie-details">
        
        <!-- Title and Year Row -->
        <HBox spacing="12.0" alignment="CENTER_LEFT">
            <Label fx:id="movieTitleLabel" 
                   text="The Dark Knight" 
                   styleClass="list-movie-title"
                   HBox.hgrow="ALWAYS"/>
            <Label fx:id="movieYearLabel" text="2008" styleClass="list-movie-year"/>
        </HBox>
        
        <!-- Rating and Duration Row -->
        <HBox spacing="15.0" alignment="CENTER_LEFT">
            <HBox spacing="4.0" alignment="CENTER_LEFT" styleClass="list-rating-container">
                <FontIcon iconLiteral="fas-star" iconSize="12" styleClass="list-rating-star"/>
                <Label fx:id="ratingLabel" text="8.5" styleClass="list-rating-text"/>
            </HBox>
            <Label fx:id="movieDurationLabel" text="152 min" styleClass="list-movie-duration"/>
            <Region HBox.hgrow="ALWAYS"/>
        </HBox>
        
        <!-- Genres -->
        <FlowPane fx:id="genreContainer" 
                 hgap="6.0" 
                 vgap="4.0"
                 styleClass="list-genre-container">
            <!-- Genre tags will be added dynamically -->
        </FlowPane>
        
        <!-- Overview -->
        <Label fx:id="movieOverviewLabel" 
               text="When the menace known as the Joker wreaks havoc and chaos on the people of Gotham, Batman must accept one of the greatest psychological and physical tests of his ability to fight injustice." 
               styleClass="list-movie-overview"
               wrapText="true"
               maxHeight="40.0"/>
        
        <!-- Director and Cast -->
        <VBox spacing="3.0">
            <HBox spacing="8.0" alignment="CENTER_LEFT">
                <Label text="Director:" styleClass="list-info-label"/>
                <Label fx:id="directorLabel" text="Christopher Nolan" styleClass="list-info-value"/>
            </HBox>
            <HBox spacing="8.0" alignment="CENTER_LEFT">
                <Label text="Cast:" styleClass="list-info-label"/>
                <Label fx:id="castLabel" 
                       text="Christian Bale, Heath Ledger, Aaron Eckhart, Michael Caine" 
                       styleClass="list-info-value"
                       wrapText="true"/>
            </HBox>
        </VBox>
    </VBox>

    <!-- Action Buttons -->
    <VBox spacing="8.0" alignment="CENTER" styleClass="list-actions">
        <Button fx:id="favoriteButton" styleClass="list-favorite-btn">
            <graphic>
                <FontIcon fx:id="favoriteIcon" iconLiteral="far-heart" iconSize="16"/>
            </graphic>
        </Button>
        
        <Button fx:id="playButton" styleClass="list-play-btn">
            <graphic>
                <FontIcon iconLiteral="fas-play" iconSize="14"/>
            </graphic>
        </Button>
        
        <Button fx:id="infoButton" styleClass="list-info-btn">
            <graphic>
                <FontIcon iconLiteral="fas-info-circle" iconSize="14"/>
            </graphic>
        </Button>
        
        <Button fx:id="addToListButton" styleClass="list-add-btn">
            <graphic>
                <FontIcon iconLiteral="fas-plus" iconSize="14"/>
            </graphic>
        </Button>
    </VBox>
</HBox>
