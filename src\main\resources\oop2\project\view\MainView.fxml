<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import org.kordamp.ikonli.javafx.FontIcon?>
<?import javafx.scene.layout.BorderPane?>
<?import javafx.scene.layout.Pane?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.layout.VBox?>

<?scenebuilder-stylesheet ../css/MainView.css?>

<BorderPane prefHeight="600.0" prefWidth="800.0"
            xmlns="http://javafx.com/javafx/17.0.12"
            xmlns:fx="http://javafx.com/fxml/1"
            fx:controller="oop2.project.controller.MainViewController">
    <!-- Sidebar -->
    <left>
        <VBox prefWidth="200.0" style="-fx-background-color: #f0f0f0; -fx-padding: 10;">
            <BorderPane.margin>
                <Insets/>
            </BorderPane.margin>
            <padding>
                <Insets bottom="10.0" left="10.0" right="10.0" top="10.0"/>
            </padding>
            <Label text="OOP2 Projekt" style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-padding: 0 0 20 0;" alignment="CENTER" maxWidth="Infinity"/>
            <Button fx:id="movieSearcherNavButton" maxWidth="Infinity" text="Movie Searcher" onMouseClicked="#onMovieSearcherButtonClicked">
                <graphic>
                    <FontIcon iconLiteral="fas-search" iconSize="16"/>
                </graphic>
                <VBox.margin>
                    <Insets top="10.0" bottom="5.0"/>
                </VBox.margin>
            </Button>
            <Pane VBox.vgrow="ALWAYS"/>
            <Button fx:id="settingsNavButton" maxWidth="Infinity" text="Settings" onMouseClicked="#onSettingsButtonClicked">
                <graphic>
                    <FontIcon iconLiteral="fas-cog" iconSize="16"/>
                </graphic>
                <VBox.margin>
                    <Insets top="5.0"/>
                </VBox.margin>
            </Button>
            <Button maxWidth="Infinity" text="Exit" onMouseClicked="#onExitButtonClicked">
                <graphic>
                    <FontIcon iconLiteral="fas-door-open" iconSize="16"/>
                </graphic>
                <VBox.margin>
                    <Insets top="5.0"/>
                </VBox.margin>
            </Button>
        </VBox>
    </left>

    <!-- Main Content Area -->
    <center>
        <StackPane fx:id="contentArea" style="-fx-background-color: #ffffff;">
            <BorderPane.margin>
                <Insets />
            </BorderPane.margin>
            <padding>
                <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
            </padding>
        </StackPane>
    </center>
</BorderPane>
