package oop2.project.model;

public enum Language {
    English("en-US"),
    <PERSON><PERSON><PERSON>("de-CH");

    private final String languageCode;

    Language(String languageCode) {
        this.languageCode = languageCode;
    }

    public String getLanguageCode() {
        return languageCode;
    }

    public static Language fromLanguageCode(String languageCode) {
        for (Language language : Language.values()) {
            if (language.getLanguageCode().equals(languageCode)) {
                return language;
            }
        }

        throw new IllegalArgumentException("Invalid language code: " + languageCode);
    }
}
