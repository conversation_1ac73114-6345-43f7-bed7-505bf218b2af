<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.*?>
<?import org.kordamp.ikonli.javafx.FontIcon?>

<?scenebuilder-stylesheet ../css/MovieCard.css?>

<VBox xmlns="http://javafx.com/javafx/17.0.12"
      xmlns:fx="http://javafx.com/fxml/1"
      styleClass="movie-card"
      prefWidth="280.0"
      maxWidth="280.0"
      spacing="0.0">

    <!-- Movie Poster Container -->
    <StackPane styleClass="poster-container">
        <!-- Movie Poster -->
        <ImageView fx:id="moviePosterImage" 
                  fitWidth="280.0" 
                  fitHeight="420.0" 
                  preserveRatio="true"
                  styleClass="movie-poster"/>
        
        <!-- Placeholder for missing poster -->
        <VBox fx:id="posterPlaceholder" 
              alignment="CENTER" 
              spacing="10.0"
              styleClass="poster-placeholder"
              visible="false">
            <FontIcon iconLiteral="fas-film" iconSize="48" styleClass="placeholder-icon"/>
            <Label text="No Image" styleClass="placeholder-text"/>
        </VBox>
        
        <!-- Rating Badge -->
        <HBox fx:id="ratingBadge" 
              alignment="CENTER" 
              spacing="4.0"
              styleClass="rating-badge"
              StackPane.alignment="TOP_RIGHT">
            <StackPane.margin>
                <Insets top="12.0" right="12.0"/>
            </StackPane.margin>
            <FontIcon iconLiteral="fas-star" iconSize="12" styleClass="rating-star"/>
            <Label fx:id="ratingLabel" text="8.5" styleClass="rating-text"/>
        </HBox>
        
        <!-- Favorite Button -->
        <Button fx:id="favoriteButton" 
                styleClass="favorite-btn"
                StackPane.alignment="TOP_LEFT">
            <StackPane.margin>
                <Insets top="12.0" left="12.0"/>
            </StackPane.margin>
            <graphic>
                <FontIcon fx:id="favoriteIcon" iconLiteral="far-heart" iconSize="16"/>
            </graphic>
        </Button>
        
        <!-- Hover Overlay -->
        <VBox fx:id="hoverOverlay" 
              alignment="CENTER" 
              spacing="15.0"
              styleClass="hover-overlay"
              visible="false">
            <Button fx:id="playButton" styleClass="play-btn">
                <graphic>
                    <FontIcon iconLiteral="fas-play" iconSize="20"/>
                </graphic>
            </Button>
            <HBox spacing="10.0" alignment="CENTER">
                <Button fx:id="infoButton" styleClass="action-btn">
                    <graphic>
                        <FontIcon iconLiteral="fas-info-circle" iconSize="16"/>
                    </graphic>
                </Button>
                <Button fx:id="addToListButton" styleClass="action-btn">
                    <graphic>
                        <FontIcon iconLiteral="fas-plus" iconSize="16"/>
                    </graphic>
                </Button>
                <Button fx:id="shareButton" styleClass="action-btn">
                    <graphic>
                        <FontIcon iconLiteral="fas-share" iconSize="16"/>
                    </graphic>
                </Button>
            </HBox>
        </VBox>
    </StackPane>

    <!-- Movie Info Section -->
    <VBox spacing="12.0" styleClass="movie-info">
        <VBox.margin>
            <Insets top="15.0" bottom="15.0" left="15.0" right="15.0"/>
        </VBox.margin>
        
        <!-- Title and Year -->
        <VBox spacing="4.0">
            <Label fx:id="movieTitleLabel" 
                   text="The Dark Knight" 
                   styleClass="movie-title"
                   wrapText="true"/>
            <HBox spacing="8.0" alignment="CENTER_LEFT">
                <Label fx:id="movieYearLabel" text="2008" styleClass="movie-year"/>
                <Label fx:id="movieDurationLabel" text="152 min" styleClass="movie-duration"/>
            </HBox>
        </VBox>
        
        <!-- Genres -->
        <FlowPane fx:id="genreContainer" 
                 hgap="6.0" 
                 vgap="6.0"
                 styleClass="genre-container">
            <!-- Genre tags will be added dynamically -->
        </FlowPane>
        
        <!-- Overview -->
        <Label fx:id="movieOverviewLabel" 
               text="When the menace known as the Joker wreaks havoc and chaos on the people of Gotham..." 
               styleClass="movie-overview"
               wrapText="true"
               maxHeight="60.0"/>
        
        <!-- Director and Cast -->
        <VBox spacing="4.0">
            <HBox spacing="6.0" alignment="CENTER_LEFT">
                <Label text="Director:" styleClass="info-label"/>
                <Label fx:id="directorLabel" text="Christopher Nolan" styleClass="info-value"/>
            </HBox>
            <HBox spacing="6.0" alignment="CENTER_LEFT">
                <Label text="Cast:" styleClass="info-label"/>
                <Label fx:id="castLabel" 
                       text="Christian Bale, Heath Ledger, Aaron Eckhart" 
                       styleClass="info-value"
                       wrapText="true"/>
            </HBox>
        </VBox>
        
        <!-- Action Buttons -->
        <HBox spacing="8.0" alignment="CENTER_LEFT" styleClass="action-buttons">
            <Button fx:id="watchNowButton" text="Watch Now" styleClass="primary-btn">
                <graphic>
                    <FontIcon iconLiteral="fas-play" iconSize="12"/>
                </graphic>
            </Button>
            <Button fx:id="addToWatchlistButton" text="Watchlist" styleClass="secondary-btn">
                <graphic>
                    <FontIcon iconLiteral="fas-bookmark" iconSize="12"/>
                </graphic>
            </Button>
        </HBox>
    </VBox>
</VBox>
