/* Navigation Button Styles */
.button {
    -fx-background-color: #3498db;
    -fx-text-fill: white;
    -fx-font-size: 14px;
    -fx-font-weight: 600;
    -fx-padding: 12 16;
    -fx-background-radius: 6;
    -fx-border-color: transparent;
    -fx-cursor: hand;
}

.button:hover {
    -fx-background-color: #2980b9;
}

.button:pressed {
    -fx-background-color: #21618c;
}

/* Disabled <PERSON><PERSON> Styles */
.button:disabled {
    -fx-background-color: #bdc3c7;
    -fx-text-fill: #7f8c8d;
    -fx-cursor: default;
    -fx-opacity: 0.6;
}

.button:disabled .ikonli-font-icon {
    -fx-icon-color: #7f8c8d;
}

/* Tooltip Styles */
.tooltip {
    -fx-background-color: #2c3e50;
    -fx-text-fill: white;
    -fx-font-size: 12px;
    -fx-padding: 8 12;
    -fx-background-radius: 4;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 4, 0, 0, 2);
}