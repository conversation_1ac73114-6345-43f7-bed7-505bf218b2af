/* Movie List View Styles */

/* Root Container */
.movie-list-container {
    -fx-background-color: #fafafa;
}

/* Header Section */
.header-section {
    -fx-background-color: white;
    -fx-background-radius: 12;
    -fx-padding: 20;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2);
}

.page-title {
    -fx-font-size: 28px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
}

.movie-count {
    -fx-font-size: 14px;
    -fx-text-fill: #7f8c8d;
    -fx-font-weight: 500;
}

/* Search and Filter Bar */
.search-filter-bar {
    -fx-padding: 15 0 0 0;
}

.search-container {
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 8;
    -fx-padding: 12 15;
    -fx-border-color: #e9ecef;
    -fx-border-radius: 8;
    -fx-border-width: 1;
}

.search-container:focused-within {
    -fx-border-color: #3498db;
    -fx-background-color: white;
}

.search-icon {
    -fx-icon-color: #95a5a6;
    -fx-icon-size: 16;
}

.search-field {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-font-size: 14px;
    -fx-prompt-text-fill: #bdc3c7;
}

.search-field:focused {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
}

.clear-search-btn {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-padding: 4;
    -fx-background-radius: 4;
}

.clear-search-btn:hover {
    -fx-background-color: #ecf0f1;
}

.clear-search-btn .ikonli-font-icon {
    -fx-icon-color: #95a5a6;
}

/* Filter Controls */
.filter-combo {
    -fx-background-color: white;
    -fx-border-color: #e9ecef;
    -fx-border-radius: 8;
    -fx-background-radius: 8;
    -fx-font-size: 14px;
    -fx-padding: 8 12;
}

.filter-combo:hover {
    -fx-border-color: #3498db;
}

.filter-combo:focused {
    -fx-border-color: #3498db;
    -fx-background-color: white;
}

.filter-checkbox {
    -fx-background-color: white;
    -fx-border-color: #e9ecef;
    -fx-border-radius: 8;
    -fx-background-radius: 8;
    -fx-font-size: 14px;
    -fx-padding: 8 12;
    -fx-text-fill: #2c3e50;
}

.filter-checkbox:hover {
    -fx-border-color: #3498db;
    -fx-background-color: #f8f9fa;
}

.filter-checkbox:selected {
    -fx-background-color: #e3f2fd;
    -fx-border-color: #3498db;
}

.filter-label {
    -fx-font-size: 14px;
    -fx-text-fill: #2c3e50;
    -fx-font-weight: 500;
}

.filter-slider {
    -fx-background-color: white;
    -fx-border-color: #e9ecef;
    -fx-border-radius: 8;
    -fx-background-radius: 8;
    -fx-padding: 8 12;
}

.filter-slider .track {
    -fx-background-color: #e9ecef;
    -fx-background-radius: 4;
}

.filter-slider .thumb {
    -fx-background-color: #3498db;
    -fx-background-radius: 8;
    -fx-border-color: #2980b9;
    -fx-border-width: 1;
}

.filter-slider .thumb:hover {
    -fx-background-color: #2980b9;
}

.filter-slider .thumb:pressed {
    -fx-background-color: #1f5f8b;
}

.filter-value-label {
    -fx-font-size: 14px;
    -fx-text-fill: #3498db;
    -fx-font-weight: bold;
    -fx-min-width: 40;
    -fx-alignment: center;
}

/* View Toggle Buttons */
.view-toggle {
    -fx-background-color: white;
    -fx-border-color: #e9ecef;
    -fx-border-radius: 6;
    -fx-background-radius: 6;
    -fx-padding: 8 12;
    -fx-min-width: 40;
}

.view-toggle:hover {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #3498db;
}

.view-toggle:selected {
    -fx-background-color: #3498db;
    -fx-border-color: #3498db;
}

.view-toggle:selected .ikonli-font-icon {
    -fx-icon-color: white;
}

.view-toggle .ikonli-font-icon {
    -fx-icon-color: #7f8c8d;
}

/* Content Area */
.content-area {
    -fx-background-color: white;
    -fx-background-radius: 12;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2);
}

.movie-scroll-pane {
    -fx-background-color: transparent;
    -fx-padding: 20;
}

.movie-scroll-pane .viewport {
    -fx-background-color: transparent;
}

.movie-scroll-pane .scroll-bar:vertical {
    -fx-background-color: transparent;
    -fx-pref-width: 8;
}

.movie-scroll-pane .scroll-bar:vertical .track {
    -fx-background-color: #f1f2f6;
    -fx-background-radius: 4;
}

.movie-scroll-pane .scroll-bar:vertical .thumb {
    -fx-background-color: #ddd;
    -fx-background-radius: 4;
}

.movie-scroll-pane .scroll-bar:vertical .thumb:hover {
    -fx-background-color: #bbb;
}

/* Movie Grid */
.movie-grid {
    -fx-padding: 10;
}

.movie-list {
    -fx-padding: 10;
}

/* Loading State */
.loading-container {
    -fx-padding: 60;
}

.loading-spinner {
    -fx-progress-color: #3498db;
    -fx-pref-width: 60;
    -fx-pref-height: 60;
}

.loading-text {
    -fx-font-size: 16px;
    -fx-text-fill: #7f8c8d;
    -fx-font-weight: 500;
}

/* Empty State */
.empty-state {
    -fx-padding: 60;
}

.empty-state-icon {
    -fx-icon-color: #bdc3c7;
}

.empty-state-title {
    -fx-font-size: 24px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
}

.empty-state-subtitle {
    -fx-font-size: 16px;
    -fx-text-fill: #7f8c8d;
    -fx-line-spacing: 2;
}

.add-movie-btn {
    -fx-background-color: #3498db;
    -fx-text-fill: white;
    -fx-font-size: 14px;
    -fx-font-weight: 600;
    -fx-padding: 12 24;
    -fx-background-radius: 8;
    -fx-border-color: transparent;
    -fx-cursor: hand;
}

.add-movie-btn:hover {
    -fx-background-color: #2980b9;
}

.add-movie-btn .ikonli-font-icon {
    -fx-icon-color: white;
}

/* Error State */
.error-state {
    -fx-padding: 60;
}

.error-state-icon {
    -fx-icon-color: #e74c3c;
}

.error-state-title {
    -fx-font-size: 24px;
    -fx-font-weight: bold;
    -fx-text-fill: #e74c3c;
}

.error-state-subtitle {
    -fx-font-size: 16px;
    -fx-text-fill: #7f8c8d;
    -fx-line-spacing: 2;
}

.retry-btn {
    -fx-background-color: #e74c3c;
    -fx-text-fill: white;
    -fx-font-size: 14px;
    -fx-font-weight: 600;
    -fx-padding: 12 24;
    -fx-background-radius: 8;
    -fx-border-color: transparent;
    -fx-cursor: hand;
}

.retry-btn:hover {
    -fx-background-color: #c0392b;
}

.retry-btn .ikonli-font-icon {
    -fx-icon-color: white;
}

/* Pagination */
.pagination-section {
    -fx-background-color: white;
    -fx-background-radius: 12;
    -fx-padding: 15 20;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2);
}

.pagination-btn {
    -fx-background-color: white;
    -fx-border-color: #e9ecef;
    -fx-border-radius: 6;
    -fx-background-radius: 6;
    -fx-padding: 8 12;
    -fx-min-width: 40;
    -fx-cursor: hand;
}

.pagination-btn:hover {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #3498db;
}

.pagination-btn:disabled {
    -fx-opacity: 0.5;
    -fx-cursor: default;
}

.pagination-btn .ikonli-font-icon {
    -fx-icon-color: #7f8c8d;
}

.pagination-label {
    -fx-font-size: 14px;
    -fx-text-fill: #7f8c8d;
    -fx-font-weight: 500;
}

.pagination-combo {
    -fx-background-color: white;
    -fx-border-color: #e9ecef;
    -fx-border-radius: 6;
    -fx-background-radius: 6;
    -fx-font-size: 14px;
    -fx-padding: 6 10;
}

.pagination-combo:hover {
    -fx-border-color: #3498db;
}

/* Page Number Buttons */
.page-number-btn {
    -fx-background-color: white;
    -fx-border-color: #e9ecef;
    -fx-border-radius: 6;
    -fx-background-radius: 6;
    -fx-padding: 8 12;
    -fx-min-width: 40;
    -fx-font-size: 14px;
    -fx-cursor: hand;
}

.page-number-btn:hover {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #3498db;
}

.page-number-btn.current-page {
    -fx-background-color: #3498db;
    -fx-border-color: #3498db;
    -fx-text-fill: white;
    -fx-font-weight: 600;
}
