package oop2.project.controller;

import javafx.fxml.FXML;
import javafx.scene.control.ComboBox;
import oop2.project.config.AppConfig;
import oop2.project.model.Language;

public class MovieSearcherController {

    @FXML
    ComboBox<Language> languageFilterCombo;

    @FXML
    public void initialize() {

        var appConfig = AppConfig.getInstance();

        languageFilterCombo.getItems().addAll(Language.values());
        languageFilterCombo.setValue(Language.fromLanguageCode(appConfig.get(AppConfig.KEY_LANGUAGE)));
    }
}
