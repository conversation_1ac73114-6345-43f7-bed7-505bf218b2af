/* Movie List Item Styles */

/* Main List Item Container */
.movie-list-item {
    -fx-background-color: white;
    -fx-background-radius: 12;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2);
    -fx-cursor: hand;
    -fx-border-color: #f1f2f6;
    -fx-border-width: 1;
    -fx-border-radius: 12;
}

.movie-list-item:hover {
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 12, 0, 0, 4);
    -fx-border-color: #e9ecef;
    -fx-background-color: #fafbfc;
}

/* Poster Container */
.list-poster-container {
    -fx-background-radius: 8;
    -fx-min-width: 80;
    -fx-max-width: 80;
    -fx-min-height: 120;
    -fx-max-height: 120;
}

.list-movie-poster {
    -fx-background-radius: 8;
    -fx-clip: true;
}

.list-poster-placeholder {
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 8;
    -fx-min-width: 80;
    -fx-max-width: 80;
    -fx-min-height: 120;
    -fx-max-height: 120;
}

.list-placeholder-icon {
    -fx-icon-color: #bdc3c7;
}

/* Movie Details */
.movie-details {
    -fx-min-width: 0;
}

.list-movie-title {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
    -fx-wrap-text: true;
}

.list-movie-year {
    -fx-font-size: 14px;
    -fx-text-fill: #7f8c8d;
    -fx-font-weight: 500;
    -fx-min-width: 50;
}

/* Rating Container */
.list-rating-container {
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 12;
    -fx-padding: 4 8;
}

.list-rating-star {
    -fx-icon-color: #f39c12;
}

.list-rating-text {
    -fx-text-fill: #2c3e50;
    -fx-font-size: 12px;
    -fx-font-weight: 600;
}

.list-movie-duration {
    -fx-font-size: 13px;
    -fx-text-fill: #95a5a6;
    -fx-font-weight: 400;
}

/* Genre Container */
.list-genre-container {
    -fx-max-height: 30;
}

.list-genre-tag {
    -fx-background-color: #ecf0f1;
    -fx-background-radius: 10;
    -fx-padding: 3 6;
    -fx-font-size: 10px;
    -fx-text-fill: #34495e;
    -fx-font-weight: 500;
}

.list-genre-tag.action {
    -fx-background-color: #ffe5e5;
    -fx-text-fill: #e74c3c;
}

.list-genre-tag.comedy {
    -fx-background-color: #fff3cd;
    -fx-text-fill: #f39c12;
}

.list-genre-tag.drama {
    -fx-background-color: #e8f4fd;
    -fx-text-fill: #3498db;
}

.list-genre-tag.thriller {
    -fx-background-color: #f0e6ff;
    -fx-text-fill: #9b59b6;
}

.list-genre-tag.horror {
    -fx-background-color: #ffe6e6;
    -fx-text-fill: #e74c3c;
}

.list-genre-tag.romance {
    -fx-background-color: #ffe6f0;
    -fx-text-fill: #e91e63;
}

.list-genre-tag.sci-fi {
    -fx-background-color: #e6f3ff;
    -fx-text-fill: #2196f3;
}

/* Movie Overview */
.list-movie-overview {
    -fx-font-size: 13px;
    -fx-text-fill: #7f8c8d;
    -fx-line-spacing: 1;
}

/* Info Labels */
.list-info-label {
    -fx-font-size: 12px;
    -fx-text-fill: #95a5a6;
    -fx-font-weight: 600;
    -fx-min-width: 60;
}

.list-info-value {
    -fx-font-size: 12px;
    -fx-text-fill: #34495e;
    -fx-font-weight: 400;
    -fx-wrap-text: true;
}

/* Action Buttons */
.list-actions {
    -fx-min-width: 60;
}

.list-favorite-btn,
.list-play-btn,
.list-info-btn,
.list-add-btn {
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 20;
    -fx-border-color: #e9ecef;
    -fx-border-radius: 20;
    -fx-border-width: 1;
    -fx-padding: 8;
    -fx-min-width: 36;
    -fx-min-height: 36;
    -fx-cursor: hand;
}

.list-favorite-btn:hover,
.list-play-btn:hover,
.list-info-btn:hover,
.list-add-btn:hover {
    -fx-background-color: white;
    -fx-border-color: #bdc3c7;
}

.list-favorite-btn .ikonli-font-icon {
    -fx-icon-color: #e74c3c;
}

.list-favorite-btn.favorited .ikonli-font-icon {
    -fx-icon-literal: "fas-heart";
}

.list-play-btn {
    -fx-background-color: #e74c3c;
    -fx-border-color: #e74c3c;
}

.list-play-btn:hover {
    -fx-background-color: #c0392b;
    -fx-border-color: #c0392b;
}

.list-play-btn .ikonli-font-icon {
    -fx-icon-color: white;
}

.list-info-btn .ikonli-font-icon {
    -fx-icon-color: #3498db;
}

.list-add-btn .ikonli-font-icon {
    -fx-icon-color: #27ae60;
}

/* Responsive Design */
@media (max-width: 768px) {
    .movie-list-item {
        -fx-spacing: 15;
    }
    
    .list-poster-container {
        -fx-min-width: 60;
        -fx-max-width: 60;
        -fx-min-height: 90;
        -fx-max-height: 90;
    }
    
    .list-movie-poster {
        -fx-fit-width: 60;
        -fx-fit-height: 90;
    }
    
    .list-poster-placeholder {
        -fx-min-width: 60;
        -fx-max-width: 60;
        -fx-min-height: 90;
        -fx-max-height: 90;
    }
    
    .list-movie-title {
        -fx-font-size: 16px;
    }
    
    .list-actions {
        -fx-min-width: 50;
    }
    
    .list-favorite-btn,
    .list-play-btn,
    .list-info-btn,
    .list-add-btn {
        -fx-min-width: 32;
        -fx-min-height: 32;
        -fx-padding: 6;
    }
}

/* Alternate row styling */
.movie-list-item:nth-child(even) {
    -fx-background-color: #fafbfc;
}

.movie-list-item:nth-child(even):hover {
    -fx-background-color: #f1f3f4;
}

/* Selection state */
.movie-list-item.selected {
    -fx-border-color: #3498db;
    -fx-border-width: 2;
    -fx-background-color: #e8f4fd;
}

.movie-list-item.selected:hover {
    -fx-background-color: #dbeafe;
}
