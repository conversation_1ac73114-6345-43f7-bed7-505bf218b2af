package oop2.project.controller;

import javafx.fxml.*;
import javafx.scene.*;
import javafx.scene.control.Button;
import javafx.scene.control.Tooltip;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.*;
import oop2.project.Main;
import oop2.project.config.AppConfig;

import java.io.IOException;

public class MainViewController {

    @FXML
    private StackPane contentArea;
    
    @FXML
    private Button movieSearcherNavButton;

    @FXML
    private Button settingsNavButton;

    @FXML
    public void initialize() {
        // Update button state based on config validity
        updateMovieSearcherButtonState();

        if(AppConfig.getInstance().isValid()) {
            showMovieSearcher();
        } else {
            showSettingsView();
        }
    }

    @FXML
    private void onMovieSearcherButtonClicked() {
        // Only allow navigation if config is valid
        if (AppConfig.getInstance().isValid()) {
            showMovieSearcher();
        }
    }

    private void showMovieSearcher() {
        try {
            FXMLLoader loader = new FXMLLoader(Main.class.getResource("view/MovieSearcher.fxml"));
            Parent movieListView = loader.load();
            contentArea.getChildren().clear();
            contentArea.getChildren().add(movieListView);
        } catch (IOException e) {
            System.out.println("Error loading movie list view " + e.getLocalizedMessage());
        }
    }

    @FXML
    private void onSettingsButtonClicked() {
        showSettingsView();
    }

    private void showSettingsView() {
        try {
            FXMLLoader loader = new FXMLLoader(Main.class.getResource("view/SettingsView.fxml"));
            Parent settings = loader.load();

            // Get the settings controller to set up communication
            SettingsViewController settingsController = loader.getController();
            settingsController.setMainViewController(this);

            contentArea.getChildren().clear();
            contentArea.getChildren().add(settings);
        } catch (IOException e) {
            // TODO: Logging
            System.out.println( "Error loading settings view" + e.getLocalizedMessage());
        }
    }

    /**
     * Called by SettingsViewController when settings are updated
     */
    public void onSettingsUpdated() {
        updateMovieSearcherButtonState();
    }

    private void updateMovieSearcherButtonState() {
        boolean isValid = AppConfig.getInstance().isValid();

        movieSearcherNavButton.setDisable(!isValid);

        if (isValid) {
            movieSearcherNavButton.setTooltip(null);
        } else {
            Tooltip tooltip = new Tooltip("API keys are required. Please configure them in Settings.");
            tooltip.setShowDelay(javafx.util.Duration.millis(300));
            movieSearcherNavButton.setTooltip(tooltip);
        }
    }

    @FXML
    private void onExitButtonClicked(MouseEvent mouseEvent) {
        System.exit(0);
    }
}
